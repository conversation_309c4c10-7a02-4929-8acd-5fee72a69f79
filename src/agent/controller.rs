use crate::agent::state::AgentStateManager;
use crate::llm::{LlmClient, ToolChoice};
use crate::llm::types::StreamEvent;
use crate::tools::{ToolExecutor, ToolRegistry};
use crate::types::{
    AgentConfig, AgentState, Message, ToolCall, ToolCallResult,
};
use crate::{AgentError, Result};
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use uuid::Uuid;
use tokio_stream::StreamExt;

/// Agent 主控制器，负责整个 Agent 系统的核心逻辑
pub struct Agent {
    config: AgentConfig,
    llm_client: LlmClient,
    tool_registry: Arc<ToolRegistry>,
    tool_executor: ToolExecutor,
    state_manager: AgentStateManager,
}

impl Agent {
    /// 创建新的 Agent 实例
    pub fn new(config: AgentConfig, tool_registry: ToolRegistry) -> Self {
        let llm_client = LlmClient::new(config.llm.clone());
        let tool_registry = Arc::new(tool_registry);
        let tool_executor = ToolExecutor::new(Arc::clone(&tool_registry));
        let state_manager = AgentStateManager::new();

        Self {
            config,
            llm_client,
            tool_registry,
            tool_executor,
            state_manager,
        }
    }

    /// 开始新的对话
    pub fn start_conversation(&mut self) -> Uuid {
        let conversation_id = self.state_manager.create_conversation();
        
        // 添加系统消息
        if let Some(system_prompt) = &self.config.system_prompt {
            let system_message = Message::new_system(system_prompt.clone());
            if let Err(e) = self.state_manager.add_message_to_current(system_message) {
                error!("Failed to add system message: {}", e);
            }
        }

        info!("Started new conversation: {}", conversation_id);
        conversation_id
    }

    /// 处理用户输入
    pub async fn process_user_input(&mut self, input: String) -> Result<String> {
        info!("Processing user input: {}", input);

        // 确保有当前对话
        if self.state_manager.get_current_conversation().is_none() {
            self.start_conversation();
        }

        // 添加用户消息
        let user_message = Message::new_user(input);
        self.state_manager.add_message_to_current(user_message)
            .map_err(|e| AgentError::agent_state(e))?;

        // 设置状态为处理中
        self.state_manager.set_current_state(AgentState::Processing)
            .map_err(|e| AgentError::agent_state(e))?;

        // 执行主要的推理循环
        let response = self.reasoning_loop().await?;

        // 设置状态为完成
        self.state_manager.set_current_state(AgentState::Completed)
            .map_err(|e| AgentError::agent_state(e))?;

        info!("Completed processing user input");
        Ok(response)
    }

    /// 流式处理用户输入
    pub async fn process_user_input_stream<F>(&mut self, input: String, mut callback: F) -> Result<String>
    where
        F: FnMut(StreamEvent) + Send,
    {
        info!("Processing user input with streaming: {}", input);

        // 确保有当前对话
        if self.state_manager.get_current_conversation().is_none() {
            self.start_conversation();
        }

        // 添加用户消息
        let user_message = Message::new_user(input);
        self.state_manager.add_message_to_current(user_message)
            .map_err(|e| AgentError::agent_state(e))?;

        // 设置状态为处理中
        self.state_manager.set_current_state(AgentState::Processing)
            .map_err(|e| AgentError::agent_state(e))?;

        // 执行流式推理循环
        let response = self.reasoning_loop_stream(&mut callback).await?;

        // 设置状态为完成
        self.state_manager.set_current_state(AgentState::Completed)
            .map_err(|e| AgentError::agent_state(e))?;

        callback(StreamEvent::Done);
        info!("Completed streaming processing user input");
        Ok(response)
    }

    /// 主要的推理循环
    async fn reasoning_loop(&mut self) -> Result<String> {
        let mut iteration = 0;
        let max_iterations = self.config.max_iterations;

        while iteration < max_iterations {
            iteration += 1;
            info!("Reasoning loop iteration: {}/{}", iteration, max_iterations);

            // 获取当前对话的所有消息
            let messages = self.get_conversation_messages()?;

            // 获取工具定义
            let tool_definitions = self.tool_registry.get_tool_definitions();

            // 调用 LLM
            let llm_response = self.llm_client
                .chat_completion(
                    messages,
                    if tool_definitions.is_empty() { None } else { Some(tool_definitions) },
                    Some(ToolChoice::Auto),
                )
                .await?;

            // 处理 LLM 响应
            let assistant_message = self.process_llm_response(llm_response).await?;

            // 如果没有工具调用，说明对话结束
            if assistant_message.tool_calls.is_none() {
                return Ok(assistant_message.content);
            }

            // 如果有工具调用但已达到最大迭代次数，返回错误
            if iteration >= max_iterations {
                warn!("Reached maximum iterations with pending tool calls");
                return Err(AgentError::agent_state(
                    "Reached maximum iterations with pending tool calls".to_string(),
                ));
            }
        }

        Err(AgentError::agent_state(
            "Reasoning loop completed without final response".to_string(),
        ))
    }

    /// 流式推理循环
    async fn reasoning_loop_stream<F>(&mut self, callback: &mut F) -> Result<String>
    where
        F: FnMut(StreamEvent) + Send,
    {
        let mut iteration = 0;
        let max_iterations = self.config.max_iterations;

        while iteration < max_iterations {
            iteration += 1;
            info!("Streaming reasoning loop iteration: {}/{}", iteration, max_iterations);

            // 获取当前对话的所有消息
            let messages = self.get_conversation_messages()?;

            // 获取工具定义
            let tool_definitions = self.tool_registry.get_tool_definitions();

            // 调用流式 LLM
            let mut stream = self.llm_client
                .chat_completion_stream(
                    messages,
                    if tool_definitions.is_empty() { None } else { Some(tool_definitions) },
                    Some(ToolChoice::Auto),
                )
                .await?;

            // 处理流式响应
            let assistant_message = self.process_llm_stream(&mut stream, callback).await?;

            // 如果没有工具调用，说明对话结束
            if assistant_message.tool_calls.is_none() {
                return Ok(assistant_message.content);
            }

            // 如果有工具调用但已达到最大迭代次数，返回错误
            if iteration >= max_iterations {
                warn!("Reached maximum iterations with pending tool calls");
                return Err(AgentError::agent_state(
                    "Reached maximum iterations with pending tool calls".to_string(),
                ));
            }
        }

        Err(AgentError::agent_state(
            "Streaming reasoning loop completed without final response".to_string(),
        ))
    }

    /// 处理 LLM 响应
    async fn process_llm_response(
        &mut self,
        llm_response: crate::llm::LlmResponse,
    ) -> Result<Message> {
        let llm_message = llm_response.message;

        // 创建 Assistant 消息
        let mut assistant_message = Message::new_assistant(
            llm_message.content.unwrap_or_default(),
        );

        // 处理工具调用
        if let Some(llm_tool_calls) = llm_message.tool_calls {
            let tool_calls: Vec<ToolCall> = llm_tool_calls
                .into_iter()
                .map(|call| call.into())
                .collect();

            debug!("Processing {} tool calls", tool_calls.len());

            // 验证工具调用
            self.tool_executor.validate_tool_calls(&tool_calls)?;

            // 执行工具调用
            let tool_results = self.tool_executor.execute_tool_calls(&tool_calls).await;

            // 设置工具调用和结果
            assistant_message.tool_calls = Some(tool_calls);
            assistant_message.tool_call_results = Some(tool_results.clone());

            // 添加 Assistant 消息到对话
            self.state_manager.add_message_to_current(assistant_message.clone())
                .map_err(|e| AgentError::agent_state(e))?;

            // 为每个工具调用结果创建工具消息
            for result in tool_results {
                let tool_message = self.create_tool_message(result)?;
                self.state_manager.add_message_to_current(tool_message)
                    .map_err(|e| AgentError::agent_state(e))?;
            }
        } else {
            // 没有工具调用，直接添加消息
            self.state_manager.add_message_to_current(assistant_message.clone())
                .map_err(|e| AgentError::agent_state(e))?;
        }

        Ok(assistant_message)
    }

    /// 处理流式 LLM 响应
    async fn process_llm_stream<F>(
        &mut self,
        stream: &mut crate::llm::types::LlmStream,
        callback: &mut F,
    ) -> Result<Message>
    where
        F: FnMut(StreamEvent) + Send,
    {
        let mut content = String::new();
        let mut tool_calls: Vec<ToolCall> = Vec::new();
        let mut current_tool_call: Option<(String, String, String)> = None; // (id, name, args)

        while let Some(chunk_result) = stream.next().await {
            match chunk_result {
                Ok(chunk) => {
                    if let Some(choice) = chunk.choices.first() {
                        let delta = &choice.delta;

                        // 处理内容
                        if let Some(delta_content) = &delta.content {
                            content.push_str(delta_content);
                            callback(StreamEvent::Content(delta_content.clone()));
                        }

                        // 处理工具调用
                        if let Some(delta_tool_calls) = &delta.tool_calls {
                            for delta_tool_call in delta_tool_calls {
                                if let Some(id) = &delta_tool_call.id {
                                    // 新的工具调用开始
                                    if let Some((prev_id, prev_name, prev_args)) = current_tool_call.take() {
                                        // 完成前一个工具调用
                                        let tool_call = ToolCall {
                                            id: prev_id.clone(),
                                            name: prev_name.clone(),
                                            parameters: serde_json::from_str(&prev_args)
                                                .unwrap_or(serde_json::Value::Object(serde_json::Map::new())),
                                        };
                                        tool_calls.push(tool_call);
                                        callback(StreamEvent::ToolCallEnd { id: prev_id });
                                    }

                                    let name = delta_tool_call.function.as_ref()
                                        .and_then(|f| f.name.as_ref())
                                        .unwrap_or(&"unknown".to_string())
                                        .clone();

                                    callback(StreamEvent::ToolCallStart {
                                        id: id.clone(),
                                        name: name.clone()
                                    });

                                    current_tool_call = Some((id.clone(), name, String::new()));
                                }

                                // 添加参数
                                if let Some(function) = &delta_tool_call.function {
                                    if let Some(args) = &function.arguments {
                                        if let Some((id, _name, ref mut current_args)) = &mut current_tool_call {
                                            current_args.push_str(args);
                                            callback(StreamEvent::ToolCallArguments {
                                                id: id.clone(),
                                                arguments: args.clone()
                                            });
                                        }
                                    }
                                }
                            }
                        }

                        // 检查是否完成
                        if choice.finish_reason.is_some() {
                            // 完成最后一个工具调用
                            if let Some((id, name, args)) = current_tool_call.take() {
                                let tool_call = ToolCall {
                                    id: id.clone(),
                                    name: name.clone(),
                                    parameters: serde_json::from_str(&args)
                                        .unwrap_or(serde_json::Value::Object(serde_json::Map::new())),
                                };
                                tool_calls.push(tool_call);
                                callback(StreamEvent::ToolCallEnd { id });
                            }
                            break;
                        }
                    }
                }
                Err(e) => {
                    if e.to_string().contains("Stream completed") {
                        break;
                    }
                    callback(StreamEvent::Error(e.to_string()));
                    return Err(e);
                }
            }
        }

        // 创建 Assistant 消息
        let mut assistant_message = Message::new_assistant(content);

        // 处理工具调用
        if !tool_calls.is_empty() {
            debug!("Processing {} tool calls from stream", tool_calls.len());

            // 验证工具调用
            self.tool_executor.validate_tool_calls(&tool_calls)?;

            // 执行工具调用
            let tool_results = self.tool_executor.execute_tool_calls(&tool_calls).await;

            // 设置工具调用和结果
            assistant_message.tool_calls = Some(tool_calls);
            assistant_message.tool_call_results = Some(tool_results.clone());

            // 添加 Assistant 消息到对话
            self.state_manager.add_message_to_current(assistant_message.clone())
                .map_err(|e| AgentError::agent_state(e))?;

            // 为每个工具调用结果创建工具消息
            for result in tool_results {
                let tool_message = self.create_tool_message(result)?;
                self.state_manager.add_message_to_current(tool_message)
                    .map_err(|e| AgentError::agent_state(e))?;
            }
        } else {
            // 没有工具调用，直接添加消息
            self.state_manager.add_message_to_current(assistant_message.clone())
                .map_err(|e| AgentError::agent_state(e))?;
        }

        Ok(assistant_message)
    }

    /// 创建工具消息
    fn create_tool_message(&self, tool_result: ToolCallResult) -> Result<Message> {
        let content = match tool_result.result {
            crate::types::ToolExecutionResult::Success { output } => {
                serde_json::to_string(&output).unwrap_or_else(|_| output.to_string())
            }
            crate::types::ToolExecutionResult::Error { error } => {
                error
            }
        };

        let message = Message::new_tool(content, tool_result.tool_call_id);

        Ok(message)
    }

    /// 获取当前对话的所有消息
    fn get_conversation_messages(&self) -> Result<Vec<Message>> {
        match self.state_manager.get_current_conversation() {
            Some(conversation) => Ok(conversation.messages.clone()),
            None => Err(AgentError::agent_state("No current conversation")),
        }
    }

    /// 获取工具注册表
    pub fn get_tool_registry(&self) -> &ToolRegistry {
        &self.tool_registry
    }

    /// 获取当前对话状态
    pub fn get_current_state(&self) -> Option<&AgentState> {
        self.state_manager.get_current_state()
    }

    /// 获取对话历史
    pub fn get_conversation_history(&self) -> Option<Vec<Message>> {
        self.state_manager
            .get_current_conversation()
            .map(|conv| conv.messages.clone())
    }

    /// 添加消息到当前对话
    pub fn add_message_to_current_conversation(&mut self, message: Message) -> Result<()> {
        self.state_manager.add_message_to_current(message)
            .map_err(|e| AgentError::agent_state(e))
    }
}
