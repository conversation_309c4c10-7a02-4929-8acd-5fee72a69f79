use crate::agent::context::MinimalAgentContext;
use crate::agent::graph_builder::MinimalAgentGraphBuilder;
use crate::agent::state::MinimalAgentState;
use crate::llm::LlmClient;
use crate::tools::ToolRegistry;
use crate::types::AgentConfig;
use framework::agent::agent::ReActAgent;

use framework::core::checkpoint::CheckPointer;
use framework::core::error::AgentError;
use std::sync::Arc;

/// 简化的 Agent 构建器
pub struct MinimalAgentBuilder {
    name: Option<String>,
    description: Option<String>,
    config: Option<AgentConfig>,
    tool_registry: Option<ToolRegistry>,
}

impl MinimalAgentBuilder {
    pub fn new() -> Self {
        Self {
            name: None,
            description: None,
            config: None,
            tool_registry: None,
        }
    }

    pub fn with_name<S: Into<String>>(mut self, name: S) -> Self {
        self.name = Some(name.into());
        self
    }

    pub fn with_description<S: Into<String>>(mut self, description: S) -> Self {
        self.description = Some(description.into());
        self
    }

    pub fn with_config(mut self, config: AgentConfig) -> Self {
        self.config = Some(config);
        self
    }

    pub fn with_tool_registry(mut self, tool_registry: ToolRegistry) -> Self {
        self.tool_registry = Some(tool_registry);
        self
    }

    pub fn build(self) -> Result<MinimalAgent, AgentError> {
        let name = self.name.unwrap_or_else(|| "MinimalAgent".to_string());
        let description = self.description.unwrap_or_else(|| "A minimal agent".to_string());
        let config = self.config.unwrap_or_default();
        let tool_registry = self.tool_registry.unwrap_or_else(|| ToolRegistry::new());

        let context = MinimalAgentContext::new(name.clone(), config, tool_registry);

        Ok(MinimalAgent {
            name,
            description,
            context: Arc::new(context),
        })
    }
}

impl Default for MinimalAgentBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 简化的 Agent 实现
pub struct MinimalAgent {
    pub name: String,
    pub description: String,
    pub context: Arc<MinimalAgentContext>,
}

impl MinimalAgent {
    pub fn builder() -> MinimalAgentBuilder {
        MinimalAgentBuilder::new()
    }

    pub fn new(name: String, config: AgentConfig, tool_registry: ToolRegistry) -> Self {
        let context = MinimalAgentContext::new(name.clone(), config, tool_registry);
        
        Self {
            name: name.clone(),
            description: format!("Agent: {}", name),
            context: Arc::new(context),
        }
    }

    /// 使用 Framework 的 ReActAgent 创建完整的 Agent
    pub fn build_framework_agent<C>(
        &self,
        check_pointer: Option<C>,
    ) -> Result<Arc<ReActAgent<LlmClient, MinimalAgentState, C>>, AgentError>
    where
        C: CheckPointer<MinimalAgentState> + 'static,
    {
        let mut builder = ReActAgent::builder()
            .with_name(self.name.clone())
            .with_description(self.description.clone())
            .with_llm(self.context.llm_client.as_ref().clone())
            .with_handle_factory(MinimalAgentGraphBuilder::new())
            .with_instruction(&self.context.system_prompt());

        if let Some(cp) = check_pointer {
            builder = builder.with_check_pointer(cp);
        }

        // 如果有工具箱，添加工具箱
        if !self.context.tool_registry.list_tools().is_empty() {
            let mut toolbox = framework::tool::tool_box::Toolbox::new();
            
            // 这里需要将我们的工具转换为 framework 的工具格式
            // 暂时跳过，后续可以实现适配器
            
            builder = builder.with_tool_box(Arc::new(toolbox));
        }

        builder.build()
    }

    /// 处理用户输入（简化版本）
    pub async fn process_input(&self, input: String) -> Result<String, AgentError> {
        // 这里可以实现简化的处理逻辑
        // 或者使用 framework 的完整流程
        
        // 暂时返回一个简单的响应
        Ok(format!("Processed: {}", input))
    }

    /// 获取 Agent 名称
    pub fn name(&self) -> &str {
        &self.name
    }

    /// 获取 Agent 描述
    pub fn description(&self) -> &str {
        &self.description
    }

    /// 获取上下文
    pub fn context(&self) -> &MinimalAgentContext {
        &self.context
    }
}

/// 无操作检查点实现（用于测试）
#[derive(Debug, Clone)]
pub struct NoOpCheckPointer;

#[async_trait::async_trait]
impl CheckPointer<MinimalAgentState> for NoOpCheckPointer {
    async fn get(&self, _session_id: &str) -> Result<framework::core::checkpoint::CheckPoint<MinimalAgentState>, framework::core::checkpoint::CheckPointError> {
        Err(framework::core::checkpoint::CheckPointError::NotFound("No checkpoints stored".to_string()))
    }

    async fn save(
        &self,
        _session_id: &str,
        _checkpoint: &framework::core::checkpoint::CheckPoint<MinimalAgentState>,
    ) -> Result<(), framework::core::checkpoint::CheckPointError> {
        // 不保存任何内容
        Ok(())
    }
}

/// 创建一个基本的 Agent 实例
pub fn create_basic_agent(
    name: String,
    config: AgentConfig,
    tool_registry: ToolRegistry,
) -> Result<MinimalAgent, AgentError> {
    MinimalAgent::builder()
        .with_name(name)
        .with_config(config)
        .with_tool_registry(tool_registry)
        .build()
}

/// 创建一个完整的 Framework Agent
pub fn create_framework_agent(
    name: String,
    config: AgentConfig,
    tool_registry: ToolRegistry,
) -> Result<Arc<ReActAgent<LlmClient, MinimalAgentState, NoOpCheckPointer>>, AgentError> {
    let minimal_agent = create_basic_agent(name, config, tool_registry)?;
    minimal_agent.build_framework_agent(Some(NoOpCheckPointer))
}
