use crate::agent::context::MinimalAgentContext;
use crate::agent::handlers::*;
use crate::agent::state::MinimalAgentState;
use framework::agent::builder::GraphBuilder;
use framework::agent::context::CallingContext;
use framework::graph::{StateGraph, CompiledGraph, GraphError};

/// Agent 图构建器
pub struct MinimalAgentGraphBuilder;

impl MinimalAgentGraphBuilder {
    pub fn new() -> Self {
        Self
    }
}

impl GraphBuilder<MinimalAgentState, MinimalAgentContext> for MinimalAgentGraphBuilder {
    fn build_graph(
        self,
    ) -> Result<CompiledGraph<MinimalAgentState, CallingContext<MinimalAgentContext>>, GraphError> {
        let mut graph = StateGraph::new();

        // 添加处理器节点
        let user_input_node = graph.add_node(UserInputHandler);
        let llm_call_node = graph.add_node(LlmCallHandler);
        let tool_execution_node = graph.add_node(ToolExecutionHandler);
        let response_node = graph.add_node(ResponseHandler);

        // 连接节点 - 构建执行流程
        // 用户输入 -> LLM 调用
        graph.connect(user_input_node, llm_call_node);

        // LLM 调用 -> 工具执行 (当有工具调用时)
        graph.connect(llm_call_node, tool_execution_node);

        // 工具执行 -> LLM 调用 (继续对话循环)
        graph.connect(tool_execution_node, llm_call_node);

        // LLM 调用 -> 响应 (当没有工具调用时)
        graph.connect(llm_call_node, response_node);

        // 设置错误处理
        graph.on_error(|_ctx, state, error| async move {
            state.set_error(error.to_string());
            Ok(())
        });

        // 设置完成处理
        graph.on_finished(|_ctx, _state, _output| async move {
            Ok(())
        });

        // 编译图
        let compiled = graph.compile()?;
        Ok(compiled)
    }
}

impl Default for MinimalAgentGraphBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 简化的图构建函数
pub fn build_minimal_agent_graph() -> Result<CompiledGraph<MinimalAgentState, CallingContext<MinimalAgentContext>>, GraphError> {
    let mut graph = StateGraph::new();

    // 创建处理器
    let user_input_handler = UserInputHandler;
    let llm_call_handler = LlmCallHandler;
    let tool_execution_handler = ToolExecutionHandler;
    let response_handler = ResponseHandler;

    // 添加节点
    let user_input_node = graph.add_node(user_input_handler);
    let llm_call_node = graph.add_node(llm_call_handler);
    let tool_execution_node = graph.add_node(tool_execution_handler);
    let response_node = graph.add_node(response_handler);

    // 连接节点
    // 基本流程: 用户输入 -> LLM 调用
    graph.connect(user_input_node, llm_call_node);
    
    // 如果有工具调用: LLM 调用 -> 工具执行 -> LLM 调用 (循环)
    graph.connect(llm_call_node, tool_execution_node);
    graph.connect(tool_execution_node, llm_call_node);
    
    // 如果没有工具调用: LLM 调用 -> 响应
    graph.connect(llm_call_node, response_node);

    // 错误处理
    graph.on_error(|_ctx, state, error| async move {
        state.set_error(error.to_string());
        tracing::error!("Agent error: {}", error);
        Ok(())
    });

    // 完成处理
    graph.on_finished(|_ctx, _state, output| async move {
        tracing::info!("Agent task completed: {:?}", output);
        Ok(())
    });

    // 编译图
    let compiled = graph.compile()?;
    Ok(compiled)
}
