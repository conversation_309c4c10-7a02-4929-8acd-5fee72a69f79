use crate::tools::ToolRegistry;
use crate::types::AgentConfig;
use framework::{
    agent::{
        builder::GraphBuilder,
        context::CallingContext,
        state::{RunState, UserState},
    },
    core::{
        agent::AgentContext,
        error::AgentError,
        event::Event,
        handler::StageHandler,
        llm::{LLMGenerateResult, LLM},
        message::Message,
    },
    graph::{router::IntoRouteValue, CompiledGraph, GraphError, StateGraph},
};
use async_trait::async_trait;
use enum_map::{enum_map, Enum};
use serde::{Deserialize, Serialize};

/// 我们的 Agent 图构建器
pub struct MinimalAgentGraph;

/// Agent 决策类型
#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum AgentDecision {
    CallTool(ToolCallAction),
    GenerateResponse(ResponseAction),
    Replan,
    Finish(String),
}

/// 工具调用动作
#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct ToolCallAction {
    pub tool_call: framework::core::message::Tool<PERSON>all,
}

/// 响应生成动作
#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct ResponseAction {
    pub content: String,
}

impl<C> GraphBuilder<RunState<UserState>, C> for MinimalAgentGraph
where
    C: AgentContext,
{
    fn build_graph(
        self,
    ) -> Result<CompiledGraph<RunState<UserState>, CallingContext<C>>, GraphError> {
        let mut graph = StateGraph::new();
        
        // 添加处理器节点
        let idle = graph.add_node(IdleHandler);
        let planning = graph.add_node(PlanningHandler);
        let tool_call = graph.add_node(ToolCallHandler);
        let response = graph.add_node(ResponseHandler);
        let shutdown = graph.add_node(ShutdownHandler);

        // 定义路由枚举
        #[derive(Clone, Enum)]
        enum Route {
            Planning,
            ToolCall,
            Response,
            Shutdown,
        }

        // 连接基本流程
        graph.connect(idle, planning);
        graph.connect(tool_call, planning);
        graph.connect(response, shutdown);

        // 添加条件路由 - 从 planning 根据决策路由到不同节点
        graph.add_conditional_edges(
            "decision_router".into(),
            planning,
            move |run_state: &mut RunState<UserState>, input: AgentDecision| {
                match input {
                    AgentDecision::CallTool(action) => {
                        (Route::ToolCall, action).into_route_value()
                    }
                    AgentDecision::GenerateResponse(action) => {
                        (Route::Response, action).into_route_value()
                    }
                    AgentDecision::Replan => {
                        if run_state.turns < 10 {
                            (Route::Planning, ()).into_route_value()
                        } else {
                            (Route::Shutdown, ()).into_route_value()
                        }
                    }
                    AgentDecision::Finish(message) => {
                        (Route::Response, ResponseAction { content: message }).into_route_value()
                    }
                }
            },
            enum_map! {
                Route::Planning => planning,
                Route::ToolCall => tool_call,
                Route::Response => response,
                Route::Shutdown => shutdown,
            },
        );

        graph.compile()
    }
}

/// Agent 上下文实现
pub struct MinimalAgentContext {
    pub name: String,
    pub config: AgentConfig,
    pub tool_registry: ToolRegistry,
}

impl MinimalAgentContext {
    pub fn new(name: String, config: AgentConfig, tool_registry: ToolRegistry) -> Self {
        Self {
            name,
            config,
            tool_registry,
        }
    }
}

impl AgentContext for MinimalAgentContext {
    type LLMType = crate::llm::LlmClient;

    fn name(&self) -> String {
        self.name.clone()
    }

    fn llm(&self) -> Result<std::sync::Arc<Self::LLMType>, framework::core::error::AgentError> {
        let llm_client = crate::llm::LlmClient::new(self.config.llm.clone());
        Ok(std::sync::Arc::new(llm_client))
    }

    fn system_prompt(&self) -> String {
        self.config.system_prompt.clone()
            .unwrap_or_else(|| "You are a helpful AI assistant.".to_string())
    }

    fn agents(&self) -> Vec<std::sync::Arc<dyn framework::core::agent::Agent>> {
        Vec::new() // 暂时不支持子 Agent
    }

    async fn call_tool(
        &self,
        tool_name: String,
        args: serde_json::Value,
    ) -> Result<framework::core::tool::CallToolResponse, framework::core::error::AgentError> {
        // 检查工具是否存在
        if let Some(tool) = self.tool_registry.get_tool(&tool_name) {
            // 创建工具调用
            let tool_call = crate::types::ToolCall {
                id: uuid::Uuid::new_v4().to_string(),
                name: tool_name,
                parameters: args,
            };

            // 执行工具
            let executor = crate::tools::ToolExecutor::new(std::sync::Arc::new(self.tool_registry.clone()));
            let result = executor.execute_tool_call(&tool_call).await;

            // 转换结果
            match result.result {
                crate::types::ToolExecutionResult::Success { output } => {
                    framework::core::tool::CallToolResponse::success(output)
                }
                crate::types::ToolExecutionResult::Error { error } => {
                    Ok(framework::core::tool::CallToolResponse::fail(
                        framework::core::error::ToolError::ExecuteError {
                            tool_name: tool_call.name,
                            message: error,
                        }
                    ))
                }
            }
        } else {
            Ok(framework::core::tool::CallToolResponse::fail(
                framework::core::error::ToolError::ToolNotFound(tool_name)
            ))
        }
    }
}

/// 创建基于 Framework 的 Agent
pub fn create_framework_agent(
    name: String,
    config: AgentConfig,
    tool_registry: ToolRegistry,
) -> Result<std::sync::Arc<framework::agent::agent::ReActAgent<crate::llm::LlmClient, UserState, super::NoOpCheckPointer>>, framework::core::error::AgentError> {
    // 创建上下文
    let context = MinimalAgentContext::new(name.clone(), config.clone(), tool_registry);
    
    // 创建 LLM 客户端
    let llm_client = crate::llm::LlmClient::new(config.llm.clone());
    
    // 构建 Agent
    framework::agent::agent::ReActAgent::builder()
        .with_name(name)
        .with_llm(llm_client)
        .with_handle_factory(MinimalAgentGraph)
        .with_instruction(&context.system_prompt())
        .with_check_pointer(super::NoOpCheckPointer)
        .build()
}

/// 简化的检查点实现
#[derive(Debug, Clone)]
pub struct NoOpCheckPointer;

#[async_trait::async_trait]
impl framework::core::checkpoint::CheckPointer<UserState> for NoOpCheckPointer {
    async fn get(&self, _session_id: &str) -> Result<framework::core::checkpoint::CheckPoint<UserState>, framework::core::checkpoint::CheckPointError> {
        Err(framework::core::checkpoint::CheckPointError::NotFound { 
            task_id: "No checkpoints stored".to_string() 
        })
    }

    async fn save(
        &self,
        _session_id: &str,
        _checkpoint: &framework::core::checkpoint::CheckPoint<UserState>,
    ) -> Result<(), framework::core::checkpoint::CheckPointError> {
        // 不保存任何内容
        Ok(())
    }
}

// ===== 处理器定义 =====

/// 空闲状态处理器 - 接收用户输入
#[derive(Debug)]
pub struct IdleHandler;

#[async_trait]
impl<C: AgentContext> StageHandler<RunState<UserState>, CallingContext<C>> for IdleHandler {
    type Incoming = ();
    type Outgoing = ();

    async fn handle(
        &self,
        ctx: CallingContext<C>,
        state: &mut RunState<UserState>,
        _input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        // 发送事件表示收到用户查询
        ctx.emit_message(Event::ResponseGenerated {
            agent_name: ctx.name(),
            response_text: format!(
                "Processing user query: '{}'",
                state.messages().last()
                    .map(|msg| msg.content())
                    .unwrap_or("No message")
            ),
        });
        Ok(())
    }
}

/// 规划状态处理器 - 调用 LLM 进行推理和决策
#[derive(Debug)]
pub struct PlanningHandler;

#[async_trait]
impl<C: AgentContext> StageHandler<RunState<UserState>, CallingContext<C>> for PlanningHandler {
    type Incoming = ();
    type Outgoing = AgentDecision;

    async fn handle(
        &self,
        ctx: CallingContext<C>,
        state: &mut RunState<UserState>,
        _input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        let llm = ctx.llm()?;
        state.turns += 1;

        // 构建消息列表
        let mut messages = Vec::new();
        messages.push(Message::new_system_message(ctx.system_prompt()));
        messages.extend_from_slice(state.messages());

        // 调用 LLM
        let resp = llm
            .generate(&messages)
            .await
            .map_err(|e| AgentError::InternalError(e.to_string()))?;

        // 创建 AI 消息
        let mut ai_message = Message::new_ai_message(resp.generation().into());
        ai_message.with_tool_calls(resp.tool_calls().map(|calls| calls.to_vec()));
        state.add_message(ai_message);

        // 检查是否有工具调用
        if let Some(tool_calls) = resp.tool_calls() {
            if let Some(tool_call) = tool_calls.first() {
                ctx.emit_message(Event::ResponseGenerated {
                    agent_name: ctx.name(),
                    response_text: format!("Calling tool: {}", tool_call.function.name),
                });

                // 检查是否是完成工具
                if tool_call.function.name == "finish_work" {
                    let message = serde_json::to_string(&tool_call.function.arguments)
                        .unwrap_or_else(|_| "Task completed".to_string());
                    return Ok(AgentDecision::Finish(message));
                }

                return Ok(AgentDecision::CallTool(ToolCallAction {
                    tool_call: tool_call.clone(),
                }));
            }
        }

        // 如果没有工具调用，生成响应
        let content = resp.generation().into();
        if !content.trim().is_empty() {
            Ok(AgentDecision::GenerateResponse(ResponseAction { content }))
        } else {
            Ok(AgentDecision::Replan)
        }
    }
}
