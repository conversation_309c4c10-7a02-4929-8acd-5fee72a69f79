use crate::types::{AgentState, Conversation, Message, Tool<PERSON>all, ToolCallResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// 基于 Framework 的 Agent 状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MinimalAgentState {
    /// 当前对话 ID
    pub conversation_id: Option<Uuid>,
    /// 对话历史
    pub conversations: HashMap<Uuid, Conversation>,
    /// 当前迭代次数
    pub iteration_count: usize,
    /// 最大迭代次数
    pub max_iterations: usize,
    /// 当前工具调用
    pub pending_tool_calls: Vec<ToolCall>,
    /// 工具调用结果
    pub tool_call_results: Vec<ToolCallResult>,
    /// 错误信息
    pub error_message: Option<String>,
}

impl Default for MinimalAgentState {
    fn default() -> Self {
        Self {
            conversation_id: None,
            conversations: HashMap::new(),
            iteration_count: 0,
            max_iterations: 10,
            pending_tool_calls: Vec::new(),
            tool_call_results: Vec::new(),
            error_message: None,
        }
    }
}

impl MinimalAgentState {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn start_conversation(&mut self) -> Uuid {
        let conversation = Conversation::new();
        let id = conversation.id;
        self.conversations.insert(id, conversation);
        self.conversation_id = Some(id);
        self.iteration_count = 0;
        self.pending_tool_calls.clear();
        self.tool_call_results.clear();
        self.error_message = None;
        id
    }

    pub fn get_current_conversation(&self) -> Option<&Conversation> {
        self.conversation_id
            .and_then(|id| self.conversations.get(&id))
    }

    pub fn get_current_conversation_mut(&mut self) -> Option<&mut Conversation> {
        self.conversation_id
            .and_then(|id| self.conversations.get_mut(&id))
    }

    pub fn add_message(&mut self, message: Message) -> Result<(), String> {
        match self.get_current_conversation_mut() {
            Some(conversation) => {
                conversation.add_message(message);
                Ok(())
            }
            None => Err("No current conversation".to_string()),
        }
    }

    pub fn increment_iteration(&mut self) {
        self.iteration_count += 1;
    }

    pub fn is_max_iterations_reached(&self) -> bool {
        self.iteration_count >= self.max_iterations
    }

    pub fn set_pending_tool_calls(&mut self, tool_calls: Vec<ToolCall>) {
        self.pending_tool_calls = tool_calls;
    }

    pub fn set_tool_call_results(&mut self, results: Vec<ToolCallResult>) {
        self.tool_call_results = results;
    }

    pub fn clear_tool_calls(&mut self) {
        self.pending_tool_calls.clear();
        self.tool_call_results.clear();
    }

    pub fn set_error(&mut self, error: String) {
        self.error_message = Some(error);
    }

    pub fn clear_error(&mut self) {
        self.error_message = None;
    }

    pub fn get_messages(&self) -> Vec<Message> {
        self.get_current_conversation()
            .map(|conv| conv.messages.clone())
            .unwrap_or_default()
    }
}

/// Agent 状态管理器（保留向后兼容性）
#[derive(Debug, Clone)]
pub struct AgentStateManager {
    conversations: HashMap<Uuid, Conversation>,
    current_conversation_id: Option<Uuid>,
}

impl AgentStateManager {
    /// 创建新的状态管理器
    pub fn new() -> Self {
        Self {
            conversations: HashMap::new(),
            current_conversation_id: None,
        }
    }

    /// 创建新的对话
    pub fn create_conversation(&mut self) -> Uuid {
        let conversation = Conversation::new();
        let id = conversation.id;
        self.conversations.insert(id, conversation);
        self.current_conversation_id = Some(id);
        id
    }

    /// 获取当前对话
    pub fn get_current_conversation(&self) -> Option<&Conversation> {
        self.current_conversation_id
            .and_then(|id| self.conversations.get(&id))
    }

    /// 获取当前对话（可变引用）
    pub fn get_current_conversation_mut(&mut self) -> Option<&mut Conversation> {
        self.current_conversation_id
            .and_then(|id| self.conversations.get_mut(&id))
    }

    /// 获取指定对话
    pub fn get_conversation(&self, id: &Uuid) -> Option<&Conversation> {
        self.conversations.get(id)
    }

    /// 获取指定对话（可变引用）
    pub fn get_conversation_mut(&mut self, id: &Uuid) -> Option<&mut Conversation> {
        self.conversations.get_mut(id)
    }

    /// 设置当前对话
    pub fn set_current_conversation(&mut self, id: Uuid) -> bool {
        if self.conversations.contains_key(&id) {
            self.current_conversation_id = Some(id);
            true
        } else {
            false
        }
    }

    /// 添加消息到当前对话
    pub fn add_message_to_current(&mut self, message: Message) -> Result<(), String> {
        match self.get_current_conversation_mut() {
            Some(conversation) => {
                conversation.add_message(message);
                Ok(())
            }
            None => Err("No current conversation".to_string()),
        }
    }

    /// 设置当前对话状态
    pub fn set_current_state(&mut self, state: AgentState) -> Result<(), String> {
        match self.get_current_conversation_mut() {
            Some(conversation) => {
                conversation.set_state(state);
                Ok(())
            }
            None => Err("No current conversation".to_string()),
        }
    }

    /// 获取当前对话状态
    pub fn get_current_state(&self) -> Option<&AgentState> {
        self.get_current_conversation().map(|conv| &conv.state)
    }

    /// 获取所有对话 ID
    pub fn get_conversation_ids(&self) -> Vec<Uuid> {
        self.conversations.keys().cloned().collect()
    }

    /// 删除对话
    pub fn remove_conversation(&mut self, id: &Uuid) -> bool {
        if self.current_conversation_id == Some(*id) {
            self.current_conversation_id = None;
        }
        self.conversations.remove(id).is_some()
    }

    /// 清除所有对话
    pub fn clear_conversations(&mut self) {
        self.conversations.clear();
        self.current_conversation_id = None;
    }

    /// 获取对话数量
    pub fn conversation_count(&self) -> usize {
        self.conversations.len()
    }
}

impl Default for AgentStateManager {
    fn default() -> Self {
        Self::new()
    }
}
