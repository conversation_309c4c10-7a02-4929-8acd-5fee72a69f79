use crate::agent::context::MinimalAgentContext;
use crate::agent::state::MinimalAgentState;
use crate::llm::framework_adapter::convert_local_message_to_framework;
use crate::types::{Message, Tool<PERSON>all, Tool<PERSON>allR<PERSON>ult, ToolExecutionResult};
use framework::core::error::AgentError;
use framework::core::handler::StageHandler;
use framework::core::llm::LLM;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::borrow::Cow;
use tracing::{debug, info};

/// 用户输入处理器 - 处理新的用户消息
#[derive(Debug)]
pub struct UserInputHandler;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInput {
    pub message: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProcessedInput {
    pub message: Message,
}

#[async_trait]
impl StageHandler<MinimalAgentState, MinimalAgentContext> for UserInputHandler {
    type Incoming = UserInput;
    type Outgoing = ProcessedInput;

    fn name(&self) -> Cow<'static, str> {
        "UserInputHandler".into()
    }

    async fn handle(
        &self,
        _ctx: MinimalAgentContext,
        state: &mut MinimalAgentState,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        info!("Processing user input: {}", input.message);

        // 确保有当前对话
        if state.conversation_id.is_none() {
            state.start_conversation();
        }

        // 创建用户消息
        let user_message = Message::new_user(input.message);
        
        // 添加到状态
        state.add_message(user_message.clone())
            .map_err(|e| AgentError::InternalError(e))?;

        // 重置迭代计数和错误状态
        state.iteration_count = 0;
        state.clear_error();
        state.clear_tool_calls();

        Ok(ProcessedInput {
            message: user_message,
        })
    }
}

/// LLM 调用处理器 - 调用语言模型生成响应
#[derive(Debug)]
pub struct LlmCallHandler;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmInput {
    pub messages: Vec<Message>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmOutput {
    pub response_message: Message,
    pub has_tool_calls: bool,
}

#[async_trait]
impl StageHandler<MinimalAgentState, MinimalAgentContext> for LlmCallHandler {
    type Incoming = LlmInput;
    type Outgoing = LlmOutput;

    fn name(&self) -> Cow<'static, str> {
        "LlmCallHandler".into()
    }

    async fn handle(
        &self,
        ctx: MinimalAgentContext,
        state: &mut MinimalAgentState,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        info!("Calling LLM with {} messages", input.messages.len());

        // 增加迭代计数
        state.increment_iteration();

        // 检查是否达到最大迭代次数
        if state.is_max_iterations_reached() {
            return Err(AgentError::InternalError(
                "Maximum iterations reached".to_string()
            ));
        }

        // 转换消息格式
        let framework_messages: Vec<_> = input.messages.iter()
            .map(|msg| convert_local_message_to_framework(msg))
            .collect();

        // 调用 LLM
        let llm = ctx.llm()?;
        let llm_result = llm.generate(&framework_messages).await
            .map_err(|e| AgentError::InternalError(e.to_string()))?;

        // 创建助手消息
        let content = llm_result.generation().into();
        let mut assistant_message = Message::new_assistant(content);

        // 检查是否有工具调用
        let has_tool_calls = if let Some(tool_calls) = llm_result.tool_calls() {
            if !tool_calls.is_empty() {
                // 转换工具调用格式
                let converted_tool_calls: Vec<ToolCall> = tool_calls.iter()
                    .map(|tc| ToolCall {
                        id: tc.id.clone(),
                        name: tc.function.name.clone(),
                        parameters: tc.function.arguments.clone(),
                    })
                    .collect();

                assistant_message.tool_calls = Some(converted_tool_calls.clone());
                state.set_pending_tool_calls(converted_tool_calls);
                true
            } else {
                false
            }
        } else {
            false
        };

        // 添加助手消息到状态
        state.add_message(assistant_message.clone())
            .map_err(|e| AgentError::InternalError(e))?;

        debug!("LLM response generated, has_tool_calls: {}", has_tool_calls);

        Ok(LlmOutput {
            response_message: assistant_message,
            has_tool_calls,
        })
    }
}

/// 工具执行处理器 - 执行工具调用
#[derive(Debug)]
pub struct ToolExecutionHandler;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolInput {
    pub tool_calls: Vec<ToolCall>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolOutput {
    pub results: Vec<ToolCallResult>,
}

#[async_trait]
impl StageHandler<MinimalAgentState, MinimalAgentContext> for ToolExecutionHandler {
    type Incoming = ToolInput;
    type Outgoing = ToolOutput;

    fn name(&self) -> Cow<'static, str> {
        "ToolExecutionHandler".into()
    }

    async fn handle(
        &self,
        ctx: MinimalAgentContext,
        state: &mut MinimalAgentState,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        info!("Executing {} tool calls", input.tool_calls.len());

        let mut results = Vec::new();

        for tool_call in &input.tool_calls {
            debug!("Executing tool: {}", tool_call.name);

            // 调用工具
            let result = ctx.call_tool(
                tool_call.name.clone(),
                tool_call.parameters.clone(),
            ).await?;

            // 转换结果格式
            let tool_result = if result.code == 0 {
                ToolCallResult {
                    tool_call_id: tool_call.id.clone(),
                    result: ToolExecutionResult::Success {
                        output: result.data.unwrap_or(serde_json::Value::Null),
                    },
                }
            } else {
                ToolCallResult {
                    tool_call_id: tool_call.id.clone(),
                    result: ToolExecutionResult::Error {
                        error: result.error_message.unwrap_or_else(|| "Unknown error".to_string()),
                    },
                }
            };

            results.push(tool_result);
        }

        // 为每个工具调用结果创建工具消息
        for result in &results {
            let content = match &result.result {
                ToolExecutionResult::Success { output } => {
                    serde_json::to_string(output).unwrap_or_else(|_| output.to_string())
                }
                ToolExecutionResult::Error { error } => error.clone(),
            };

            let tool_message = Message::new_tool(content, result.tool_call_id.clone());
            state.add_message(tool_message)
                .map_err(|e| AgentError::InternalError(e))?;
        }

        // 保存结果到状态
        state.set_tool_call_results(results.clone());

        debug!("Tool execution completed with {} results", results.len());

        Ok(ToolOutput { results })
    }
}

/// 响应生成处理器 - 生成最终响应
#[derive(Debug)]
pub struct ResponseHandler;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseInput {
    pub message: Message,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseOutput {
    pub content: String,
    pub completed: bool,
}

#[async_trait]
impl StageHandler<MinimalAgentState, MinimalAgentContext> for ResponseHandler {
    type Incoming = ResponseInput;
    type Outgoing = ResponseOutput;

    fn name(&self) -> Cow<'static, str> {
        "ResponseHandler".into()
    }

    async fn handle(
        &self,
        _ctx: MinimalAgentContext,
        state: &mut MinimalAgentState,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        info!("Generating final response");

        // 清理状态
        state.clear_tool_calls();

        Ok(ResponseOutput {
            content: input.message.content,
            completed: true,
        })
    }
}

/// 决策路由器 - 根据 LLM 输出决定下一步
#[derive(Debug)]
pub struct DecisionRouter;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecisionInput {
    pub llm_output: LlmOutput,
}

impl DecisionRouter {
    pub fn new() -> Self {
        Self
    }

    pub fn route(&self, input: DecisionInput) -> &'static str {
        if input.llm_output.has_tool_calls {
            "tool_execution"
        } else {
            "response"
        }
    }
}
