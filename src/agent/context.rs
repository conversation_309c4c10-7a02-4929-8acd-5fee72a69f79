use crate::llm::LlmClient;
use crate::tools::{ToolExecutor, ToolRegistry};
use crate::types::AgentConfig;
use framework::core::agent::AgentContext;
use framework::core::error::AgentError;
use framework::core::tool::CallToolResponse;
use framework::core::agent::Agent;
use std::sync::Arc;
use async_trait::async_trait;

/// 实现 Framework AgentContext trait 的上下文结构
pub struct MinimalAgentContext {
    pub name: String,
    pub llm_client: Arc<LlmClient>,
    pub tool_registry: Arc<ToolRegistry>,
    pub tool_executor: Arc<ToolExecutor>,
    pub config: AgentConfig,
    pub agents: Vec<Arc<dyn Agent>>,
}

impl MinimalAgentContext {
    pub fn new(
        name: String,
        config: AgentConfig,
        tool_registry: ToolRegistry,
    ) -> Self {
        let llm_client = Arc::new(LlmClient::new(config.llm.clone()));
        let tool_registry = Arc::new(tool_registry);
        let tool_executor = Arc::new(ToolExecutor::new(Arc::clone(&tool_registry)));

        Self {
            name,
            llm_client,
            tool_registry,
            tool_executor,
            config,
            agents: Vec::new(),
        }
    }

    pub fn with_agents(mut self, agents: Vec<Arc<dyn Agent>>) -> Self {
        self.agents = agents;
        self
    }
}

#[async_trait]
impl AgentContext for MinimalAgentContext {
    type LLMType = LlmClient;

    fn name(&self) -> String {
        self.name.clone()
    }

    fn llm(&self) -> Result<Arc<Self::LLMType>, AgentError> {
        Ok(Arc::clone(&self.llm_client))
    }

    fn system_prompt(&self) -> String {
        self.config.system_prompt.clone()
            .unwrap_or_else(|| "You are a helpful AI assistant.".to_string())
    }

    fn agents(&self) -> Vec<Arc<dyn Agent>> {
        self.agents.clone()
    }

    async fn call_tool(
        &self,
        tool_name: String,
        args: serde_json::Value,
    ) -> Result<CallToolResponse, AgentError> {
        // 验证工具是否存在
        if !self.tool_registry.has_tool(&tool_name) {
            return Ok(CallToolResponse::fail(
                framework::core::error::ToolError::ToolNotFound(tool_name)
            ));
        }

        // 执行工具调用
        match self.tool_executor.execute_tool(&tool_name, args).await {
            Ok(result) => {
                match result.result {
                    crate::types::ToolExecutionResult::Success { output } => {
                        CallToolResponse::success(output)
                    }
                    crate::types::ToolExecutionResult::Error { error } => {
                        Ok(CallToolResponse::fail(
                            framework::core::error::ToolError::ExecutionError(error)
                        ))
                    }
                }
            }
            Err(e) => {
                Ok(CallToolResponse::fail(
                    framework::core::error::ToolError::ExecutionError(e.to_string())
                ))
            }
        }
    }
}
