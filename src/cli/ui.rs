use crate::Agent;
use crate::cli::commands::CommandHandler;
use crate::llm::types::StreamEvent;
use indicatif::{ProgressBar, ProgressStyle};

/// CLI 用户界面管理器
pub struct CliUI {
    command_handler: CommandHandler,
}

impl CliUI {
    pub fn new() -> Self {
        Self {
            command_handler: CommandHandler::new(),
        }
    }

    /// 运行交互模式
    pub async fn run_interactive_mode(&mut self, agent: &mut Agent) -> Result<(), Box<dyn std::error::Error>> {
        // 显示欢迎信息
        let tools = agent.get_tool_registry().get_tool_names();
        self.command_handler.show_welcome(&tools);

        // 开始新对话
        let conversation_id = agent.start_conversation();
        tracing::info!("Started conversation: {}", conversation_id);

        loop {
            // 获取用户输入
            match self.command_handler.get_user_input() {
                Ok(input) => {
                    if input.is_empty() {
                        continue;
                    }

                    // 处理内置命令
                    let (is_builtin, should_continue) = self.command_handler.handle_builtin_command(&input, agent);

                    if !should_continue {
                        break; // 用户选择退出
                    }

                    // 如果不是内置命令，则发送给 Agent 处理
                    if !is_builtin {
                        self.process_agent_request_stream(agent, &input).await;
                    }
                }
                Err(e) => {
                    self.command_handler.show_error(&format!("输入错误: {}", e));
                }
            }
        }

        // 保存输入历史记录
        if let Err(e) = self.command_handler.save_history() {
            tracing::warn!("Failed to save input history: {}", e);
        }

        Ok(())
    }

    /// 运行单次查询模式
    pub async fn run_single_query_mode(&self, agent: &mut Agent) -> Result<(), Box<dyn std::error::Error>> {
        println!("🤖 Minimal Agent - 单次查询模式");
        print!("请输入您的查询: ");
        std::io::Write::flush(&mut std::io::stdout())?;

        let mut input = String::new();
        std::io::stdin().read_line(&mut input)?;
        let input = input.trim();

        if input.is_empty() {
            println!("未提供输入。");
            return Ok(());
        }

        agent.start_conversation();
        self.process_agent_request_stream(agent, input).await;

        Ok(())
    }

    /// 流式处理 Agent 请求
    async fn process_agent_request_stream(&self, agent: &mut Agent, input: &str) {
        // 开始流式响应显示
        self.command_handler.start_streaming_response();

        // 创建回调函数来处理流式事件
        let callback = |event: StreamEvent| {
            self.command_handler.handle_stream_event(event);
        };

        match agent.process_user_input_stream(input.to_string(), callback).await {
            Ok(_response) => {
                // 响应已经通过流式回调显示了，这里不需要额外处理
            }
            Err(e) => {
                self.command_handler.show_error(&format!("处理请求时出错: {}", e));
                tracing::error!("Error processing streaming input: {}", e);
            }
        }
    }

    /// 创建进度条
    fn create_progress_bar(&self, message: &str) -> ProgressBar {
        let pb = ProgressBar::new_spinner();
        pb.set_style(
            ProgressStyle::default_spinner()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"])
                .template("{spinner:.green} {msg}")
                .unwrap(),
        );
        pb.set_message(message.to_string());
        pb
    }


}

impl Default for CliUI {
    fn default() -> Self {
        Self::new()
    }
}
