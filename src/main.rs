use clap::{Arg, Command};
use minimal_agent::{
    Agent,
    agent::{MinimalAgent, create_framework_agent},
    cli::CliUI,
    config::ConfigManager,
    tools::{ToolRegistry, builtin::{
        EchoTool, FileReadTool, FileWriteTool, FileDeleteTool,
        ListDirectoryTool, FileSearchTool, CodeAnalysisTool, ProjectStructureTool,
        EditorTool
    }},
};
use std::sync::Arc;
use tracing::info;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter("minimal_agent=info")
        .init();

    // 解析命令行参数
    let matches = Command::new("Minimal Agent")
        .version("0.1.0")
        .about("A minimal agent system based on LLM-driven tool calling")
        .arg(
            Arg::new("api-key")
                .long("api-key")
                .value_name("KEY")
                .help("API key (OpenAI or OpenRouter)")
                .required(false),
        )
        .arg(
            Arg::new("provider")
                .long("provider")
                .value_name("PROVIDER")
                .help("LLM provider (openai, openrouter)")
                .default_value("openrouter"),
        )
        .arg(
            Arg::new("model")
                .long("model")
                .value_name("MODEL")
                .help("LLM model to use")
                .default_value("anthropic/claude-3.5-sonnet"),
        )
        .arg(
            Arg::new("interactive")
                .short('i')
                .long("interactive")
                .help("Run in interactive mode")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("test-mode")
                .long("test-mode")
                .help("Use cheaper model for testing (Claude-3 Haiku)")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("coding-mode")
                .long("coding-mode")
                .help("Enable coding assistant mode with specialized tools and prompts")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("clear-config")
                .long("clear-config")
                .help("清除缓存的配置")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("show-config")
                .long("show-config")
                .help("显示配置信息")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("reconfigure")
                .long("reconfigure")
                .help("重新配置（清除缓存并重新设置）")
                .action(clap::ArgAction::SetTrue),
        )
        .get_matches();

    // 处理配置管理命令
    if matches.get_flag("clear-config") {
        ConfigManager::clear_cached_config()?;
        return Ok(());
    }

    if matches.get_flag("show-config") {
        ConfigManager::show_config_stats()?;
        return Ok(());
    }

    if matches.get_flag("reconfigure") {
        let test_mode = matches.get_flag("test-mode");
        let coding_mode = matches.get_flag("coding-mode");
        let config = ConfigManager::reconfigure(test_mode, coding_mode).await?;
        println!("✅ 重新配置完成！");
        ConfigManager::display_config(&config);
        return Ok(());
    }

    // 获取配置参数
    let provider = matches.get_one::<String>("provider").unwrap().clone();
    let mut model = matches.get_one::<String>("model").unwrap().clone();
    let coding_mode = matches.get_flag("coding-mode");
    let test_mode = matches.get_flag("test-mode");

    // 如果启用测试模式，使用更便宜的模型
    if test_mode {
        model = match provider.as_str() {
            "openrouter" => "anthropic/claude-3-haiku".to_string(),
            "openai" => "gpt-3.5-turbo".to_string(),
            _ => model,
        };
        println!("🧪 测试模式: 使用模型 {}", model);
    }

    // 使用配置管理器获取配置
    let config = ConfigManager::get_config(
        Some(provider),
        Some(model),
        matches.get_one::<String>("api-key").cloned(),
        test_mode,
        coding_mode,
    ).await?;

    // 验证配置
    if let Err(e) = ConfigManager::validate_config(&config) {
        eprintln!("❌ 配置错误: {}", e);
        return Err(e.into());
    }

    // 显示配置信息
    ConfigManager::display_config(&config);

    // 创建工具注册表并注册内置工具
    let mut tool_registry = ToolRegistry::new();

    // 基础工具
    tool_registry.register_tool(Arc::new(EchoTool))?;

    // 代码编写工具（在 coding-mode 下或默认启用）
    if coding_mode || true { // 默认启用文件工具
        tool_registry.register_tool(Arc::new(FileReadTool))?;
        tool_registry.register_tool(Arc::new(FileWriteTool))?;
        tool_registry.register_tool(Arc::new(FileDeleteTool))?;
        tool_registry.register_tool(Arc::new(ListDirectoryTool))?;
        tool_registry.register_tool(Arc::new(FileSearchTool))?;
        tool_registry.register_tool(Arc::new(CodeAnalysisTool))?;
        tool_registry.register_tool(Arc::new(ProjectStructureTool))?;
        tool_registry.register_tool(Arc::new(EditorTool))?;
    }

    info!("Registered {} tools", tool_registry.tool_count());
    if coding_mode {
        info!("🔧 Coding mode enabled with file editing and code analysis tools");
    }

    // 创建 Agent - 支持两种模式
    let use_framework = std::env::var("USE_FRAMEWORK").unwrap_or_default() == "true";

    if use_framework {
        // 使用新的 Framework 架构
        println!("🚀 Using Framework-based Agent architecture");
        let framework_agent = create_framework_agent(
            "MinimalAgent".to_string(),
            config.clone(),
            tool_registry,
        )?;

        // 这里可以使用 framework_agent 进行处理
        // 暂时使用原有的 Agent 作为后备
        let mut agent = Agent::new(config, ToolRegistry::new());
        let mut cli_ui = CliUI::new();

        if matches.get_flag("interactive") {
            cli_ui.run_interactive_mode(&mut agent).await?;
        } else {
            cli_ui.run_single_query_mode(&mut agent).await?;
        }
    } else {
        // 使用原有的 Agent 架构
        println!("📝 Using legacy Agent architecture");
        let mut agent = Agent::new(config, tool_registry);
        let mut cli_ui = CliUI::new();

        if matches.get_flag("interactive") {
            cli_ui.run_interactive_mode(&mut agent).await?;
        } else {
            cli_ui.run_single_query_mode(&mut agent).await?;
        }
    }

    Ok(())
}
