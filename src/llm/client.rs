use crate::llm::types::{LlmRequest, LlmResponse, ToolChoice, LlmStream, StreamChunk};
use crate::types::{LlmConfig, Message};
use crate::tools::ToolDefinition;
use crate::{AgentError, Result};
use reqwest::Client;
use serde_json::json;
use tracing::{debug, error, info, warn};
use eventsource_stream::Eventsource;
use futures::stream::TryStreamExt;

/// LLM 客户端，负责与大语言模型 API 通信
pub struct LlmClient {
    config: LlmConfig,
    client: Client,
}

impl LlmClient {
    /// 创建新的 LLM 客户端
    pub fn new(config: LlmConfig) -> Self {
        Self {
            config,
            client: Client::new(),
        }
    }

    /// 发送聊天完成请求
    pub async fn chat_completion(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<ToolDefinition>>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<LlmResponse> {
        info!("Sending chat completion request with {} messages", messages.len());
        
        let llm_messages: Vec<_> = messages.iter().map(|m| m.into()).collect();
        
        let request = LlmRequest {
            messages: llm_messages,
            tools,
            tool_choice,
            temperature: self.config.temperature,
            max_tokens: self.config.max_tokens,
            model: self.config.model.clone(),
        };

        debug!("LLM request: {:?}", request);

        let response = match self.config.provider.as_str() {
            "openai" => self.send_openai_request(&request).await?,
            "openrouter" => self.send_openrouter_request(&request).await?,
            "anthropic" => self.send_anthropic_request(&request).await?,
            _ => {
                return Err(AgentError::configuration(format!(
                    "Unsupported LLM provider: {}",
                    self.config.provider
                )));
            }
        };

        debug!("LLM response: {:?}", response);
        info!("Received chat completion response");

        Ok(response)
    }

    /// 发送流式聊天完成请求
    pub async fn chat_completion_stream(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<ToolDefinition>>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<LlmStream> {
        info!("Sending streaming chat completion request with {} messages", messages.len());

        let llm_messages: Vec<_> = messages.iter().map(|m| m.into()).collect();

        let request = LlmRequest {
            messages: llm_messages,
            tools,
            tool_choice,
            temperature: self.config.temperature,
            max_tokens: self.config.max_tokens,
            model: self.config.model.clone(),
        };

        debug!("LLM streaming request: {:?}", request);

        let stream = match self.config.provider.as_str() {
            "openai" => self.send_openai_stream_request(&request).await?,
            "openrouter" => self.send_openrouter_stream_request(&request).await?,
            "anthropic" => self.send_anthropic_stream_request(&request).await?,
            _ => {
                return Err(AgentError::configuration(format!(
                    "Unsupported LLM provider: {}",
                    self.config.provider
                )));
            }
        };

        info!("Started streaming chat completion");
        Ok(stream)
    }

    /// 发送 OpenAI API 请求
    async fn send_openai_request(&self, request: &LlmRequest) -> Result<LlmResponse> {
        let url = self
            .config
            .base_url
            .as_ref()
            .map(|base| format!("{}/chat/completions", base))
            .unwrap_or_else(|| "https://api.openai.com/v1/chat/completions".to_string());

        let mut payload = json!({
            "model": request.model,
            "messages": request.messages,
        });

        if let Some(temp) = request.temperature {
            payload["temperature"] = json!(temp);
        }

        if let Some(max_tokens) = request.max_tokens {
            payload["max_tokens"] = json!(max_tokens);
        }

        if let Some(tools) = &request.tools {
            payload["tools"] = json!(tools.iter().map(|tool| {
                json!({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.parameters
                    }
                })
            }).collect::<Vec<_>>());

            if let Some(tool_choice) = &request.tool_choice {
                payload["tool_choice"] = match tool_choice {
                    ToolChoice::Auto => json!("auto"),
                    ToolChoice::None => json!("none"),
                    ToolChoice::Required => json!("required"),
                    ToolChoice::Specific { function } => json!({
                        "type": "function",
                        "function": {
                            "name": function.name
                        }
                    }),
                };
            }
        }

        let response = self
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("OpenAI API error: {}", error_text);
            return Err(AgentError::other(format!("OpenAI API error: {}", error_text)));
        }

        let response_json: serde_json::Value = response.json().await?;
        
        // 解析 OpenAI 响应格式
        let choice = response_json["choices"][0].clone();
        let message = choice["message"].clone();
        
        let llm_response = LlmResponse {
            message: serde_json::from_value(message)?,
            usage: response_json.get("usage").map(|u| serde_json::from_value(u.clone()).ok()).flatten(),
            finish_reason: choice.get("finish_reason").and_then(|v| v.as_str()).map(|s| s.to_string()),
        };

        Ok(llm_response)
    }

    /// 发送 OpenRouter API 请求
    async fn send_openrouter_request(&self, request: &LlmRequest) -> Result<LlmResponse> {
        let url = self
            .config
            .base_url
            .as_ref()
            .map(|base| format!("{}/chat/completions", base))
            .unwrap_or_else(|| "https://openrouter.ai/api/v1/chat/completions".to_string());

        let mut payload = json!({
            "model": request.model,
            "messages": request.messages,
        });

        if let Some(temp) = request.temperature {
            payload["temperature"] = json!(temp);
        }

        if let Some(max_tokens) = request.max_tokens {
            payload["max_tokens"] = json!(max_tokens);
        }

        if let Some(tools) = &request.tools {
            payload["tools"] = json!(tools.iter().map(|tool| {
                json!({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.parameters
                    }
                })
            }).collect::<Vec<_>>());

            if let Some(tool_choice) = &request.tool_choice {
                payload["tool_choice"] = match tool_choice {
                    ToolChoice::Auto => json!("auto"),
                    ToolChoice::None => json!("none"),
                    ToolChoice::Required => json!("required"),
                    ToolChoice::Specific { function } => json!({
                        "type": "function",
                        "function": {
                            "name": function.name
                        }
                    }),
                };
            }
        }

        let response = self
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .header("Content-Type", "application/json")
            .header("HTTP-Referer", "https://github.com/your-username/minimal_agent") // OpenRouter 要求
            .header("X-Title", "Minimal Agent") // OpenRouter 应用标识
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await?;
            error!("OpenRouter API error ({}): {}", status, error_text);

            // 提供更友好的错误信息
            let user_friendly_error = match status.as_u16() {
                401 => "API 密钥无效或未提供。请检查您的 OPENROUTER_API_KEY 环境变量。".to_string(),
                402 => "账户余额不足。请在 OpenRouter 控制台充值。".to_string(),
                403 => "API 密钥限制已达上限。请访问 https://openrouter.ai/settings/keys 管理您的密钥限制。".to_string(),
                429 => "请求过于频繁。请稍后重试。".to_string(),
                _ => format!("OpenRouter API 错误: {}", error_text),
            };

            return Err(AgentError::other(user_friendly_error));
        }

        let response_json: serde_json::Value = response.json().await?;

        // OpenRouter 使用与 OpenAI 兼容的响应格式
        let choice = response_json["choices"][0].clone();
        let message = choice["message"].clone();

        let llm_response = LlmResponse {
            message: serde_json::from_value(message)?,
            usage: response_json.get("usage").map(|u| serde_json::from_value(u.clone()).ok()).flatten(),
            finish_reason: choice.get("finish_reason").and_then(|v| v.as_str()).map(|s| s.to_string()),
        };

        Ok(llm_response)
    }

    /// 发送 Anthropic API 请求（简化实现）
    async fn send_anthropic_request(&self, _request: &LlmRequest) -> Result<LlmResponse> {
        // 这里可以实现 Anthropic Claude API 的调用
        // 目前返回一个错误，表示尚未实现
        Err(AgentError::configuration(
            "Anthropic provider not yet implemented".to_string(),
        ))
    }

    /// 发送 OpenAI 流式 API 请求
    async fn send_openai_stream_request(&self, request: &LlmRequest) -> Result<LlmStream> {
        let url = self
            .config
            .base_url
            .as_ref()
            .map(|base| format!("{}/chat/completions", base))
            .unwrap_or_else(|| "https://api.openai.com/v1/chat/completions".to_string());

        let mut payload = json!({
            "model": request.model,
            "messages": request.messages,
            "stream": true,
        });

        if let Some(temp) = request.temperature {
            payload["temperature"] = json!(temp);
        }

        if let Some(max_tokens) = request.max_tokens {
            payload["max_tokens"] = json!(max_tokens);
        }

        if let Some(tools) = &request.tools {
            payload["tools"] = json!(tools.iter().map(|tool| {
                json!({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.parameters
                    }
                })
            }).collect::<Vec<_>>());

            if let Some(tool_choice) = &request.tool_choice {
                payload["tool_choice"] = match tool_choice {
                    ToolChoice::Auto => json!("auto"),
                    ToolChoice::None => json!("none"),
                    ToolChoice::Required => json!("required"),
                    ToolChoice::Specific { function } => json!({
                        "type": "function",
                        "function": {
                            "name": function.name
                        }
                    }),
                };
            }
        }

        let response = self
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("OpenAI API error: {}", error_text);
            return Err(AgentError::other(format!("OpenAI API error: {}", error_text)));
        }

        let stream = response
            .bytes_stream()
            .eventsource()
            .map_err(|e| AgentError::other(format!("Stream error: {}", e)))
            .and_then(|event| async move {
                match event.data.as_str() {
                    "[DONE]" => Err(AgentError::other("Stream completed".to_string())),
                    data => {
                        match serde_json::from_str::<StreamChunk>(data) {
                            Ok(chunk) => Ok(chunk),
                            Err(e) => {
                                warn!("Failed to parse stream chunk: {}, data: {}", e, data);
                                Err(AgentError::other(format!("Parse error: {}", e)))
                            }
                        }
                    }
                }
            });

        Ok(Box::pin(stream))
    }

    /// 发送 OpenRouter 流式 API 请求
    async fn send_openrouter_stream_request(&self, request: &LlmRequest) -> Result<LlmStream> {
        let url = self
            .config
            .base_url
            .as_ref()
            .map(|base| format!("{}/chat/completions", base))
            .unwrap_or_else(|| "https://openrouter.ai/api/v1/chat/completions".to_string());

        let mut payload = json!({
            "model": request.model,
            "messages": request.messages,
            "stream": true,
        });

        if let Some(temp) = request.temperature {
            payload["temperature"] = json!(temp);
        }

        if let Some(max_tokens) = request.max_tokens {
            payload["max_tokens"] = json!(max_tokens);
        }

        if let Some(tools) = &request.tools {
            payload["tools"] = json!(tools.iter().map(|tool| {
                json!({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.parameters
                    }
                })
            }).collect::<Vec<_>>());

            if let Some(tool_choice) = &request.tool_choice {
                payload["tool_choice"] = match tool_choice {
                    ToolChoice::Auto => json!("auto"),
                    ToolChoice::None => json!("none"),
                    ToolChoice::Required => json!("required"),
                    ToolChoice::Specific { function } => json!({
                        "type": "function",
                        "function": {
                            "name": function.name
                        }
                    }),
                };
            }
        }

        let response = self
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .header("Content-Type", "application/json")
            .header("HTTP-Referer", "https://github.com/your-username/minimal_agent")
            .header("X-Title", "Minimal Agent")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await?;
            error!("OpenRouter API error ({}): {}", status, error_text);

            let user_friendly_error = match status.as_u16() {
                401 => "API 密钥无效或未提供。请检查您的 OPENROUTER_API_KEY 环境变量。".to_string(),
                402 => "账户余额不足。请在 OpenRouter 控制台充值。".to_string(),
                403 => "API 密钥限制已达上限。请访问 https://openrouter.ai/settings/keys 管理您的密钥限制。".to_string(),
                429 => "请求过于频繁。请稍后重试。".to_string(),
                _ => format!("OpenRouter API 错误: {}", error_text),
            };

            return Err(AgentError::other(user_friendly_error));
        }

        let stream = response
            .bytes_stream()
            .eventsource()
            .map_err(|e| AgentError::other(format!("Stream error: {}", e)))
            .and_then(|event| async move {
                match event.data.as_str() {
                    "[DONE]" => Err(AgentError::other("Stream completed".to_string())),
                    data => {
                        match serde_json::from_str::<StreamChunk>(data) {
                            Ok(chunk) => Ok(chunk),
                            Err(e) => {
                                warn!("Failed to parse stream chunk: {}, data: {}", e, data);
                                Err(AgentError::other(format!("Parse error: {}", e)))
                            }
                        }
                    }
                }
            });

        Ok(Box::pin(stream))
    }

    /// 发送 Anthropic 流式 API 请求（简化实现）
    async fn send_anthropic_stream_request(&self, _request: &LlmRequest) -> Result<LlmStream> {
        Err(AgentError::configuration(
            "Anthropic streaming not yet implemented".to_string(),
        ))
    }
}
