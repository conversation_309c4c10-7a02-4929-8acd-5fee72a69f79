use crate::llm::client::LlmClient;
use crate::llm::types::{LlmResponse, StreamChunk};
use crate::types::Message;
use framework::core::llm::{LLM, LLMGenerateResult};
use framework::core::message::{Message as FrameworkMessage, ToolCall as FrameworkToolCall, FunctionCall};
use async_trait::async_trait;
use std::pin::Pin;
use futures::{Stream, StreamExt};
use std::error::Error;
use std::fmt;

/// 错误类型适配器
#[derive(Debug)]
pub struct LlmAdapterError(pub crate::AgentError);

impl fmt::Display for LlmAdapterError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl Error for LlmAdapterError {}

impl From<crate::AgentError> for LlmAdapterError {
    fn from(err: crate::AgentError) -> Self {
        LlmAdapterError(err)
    }
}

/// LLM 响应适配器
pub struct LlmResponseAdapter {
    pub response: LlmResponse,
}

impl LLMGenerateResult for LlmResponseAdapter {
    fn generation(&self) -> impl Into<String> {
        self.response.message.content.clone().unwrap_or_default()
    }

    fn tool_calls(&self) -> Option<&[FrameworkToolCall]> {
        // 这里需要转换工具调用格式
        // 暂时返回 None，后续可以实现转换逻辑
        None
    }
}

/// Framework LLM trait 的实现
#[async_trait]
impl LLM for LlmClient {
    type Error = LlmAdapterError;
    type StreamData = StreamChunk;
    type GenerateResult = LlmResponseAdapter;

    async fn generate(&self, messages: &[FrameworkMessage]) -> Result<Self::GenerateResult, Self::Error> {
        // 转换 Framework 消息格式到我们的消息格式
        let converted_messages: Vec<Message> = messages.iter()
            .map(|msg| convert_framework_message_to_local(msg))
            .collect();

        // 调用现有的 chat_completion 方法
        let response = self.chat_completion(converted_messages, None, None).await?;
        
        Ok(LlmResponseAdapter { response })
    }

    async fn stream(
        &self,
        messages: &[FrameworkMessage],
    ) -> Result<
        Pin<Box<dyn Stream<Item = Result<Self::StreamData, Self::Error>> + Send>>,
        Self::Error,
    > {
        // 转换消息格式
        let converted_messages: Vec<Message> = messages.iter()
            .map(|msg| convert_framework_message_to_local(msg))
            .collect();

        // 调用现有的流式方法
        let stream = self.chat_completion_stream(converted_messages, None, None).await?;
        
        // 转换错误类型
        let adapted_stream = stream.map(|result| {
            result.map_err(|e| LlmAdapterError(e))
        });

        Ok(Box::pin(adapted_stream))
    }
}

/// 转换 Framework 消息到本地消息格式
fn convert_framework_message_to_local(msg: &FrameworkMessage) -> Message {
    use crate::types::MessageRole;

    let (role, content, tool_calls, tool_call_id) = match msg {
        FrameworkMessage::HumanMessage(content) => {
            (MessageRole::User, content.clone(), None, None)
        }
        FrameworkMessage::AIAssistant { content, tool_calls } => {
            let converted_tool_calls = tool_calls.as_ref().map(|tcs| {
                tcs.iter()
                    .map(|tc| crate::types::ToolCall {
                        id: tc.id.clone(),
                        name: tc.function.name.clone(),
                        parameters: tc.function.arguments.clone(),
                    })
                    .collect()
            });
            (MessageRole::Assistant, content.clone(), converted_tool_calls, None)
        }
        FrameworkMessage::SystemMessage(content) => {
            (MessageRole::System, content.clone(), None, None)
        }
        FrameworkMessage::ToolMessage { toolcall, result } => {
            let content = result.as_str().unwrap_or(&result.to_string()).to_string();
            (MessageRole::Tool, content, None, Some(toolcall.id.clone()))
        }
    };

    Message {
        id: uuid::Uuid::new_v4(),
        role,
        content,
        timestamp: chrono::Utc::now(),
        tool_calls,
        tool_call_results: None,
        tool_call_id,
    }
}

/// 转换本地消息到 Framework 消息格式
pub fn convert_local_message_to_framework(msg: &Message) -> FrameworkMessage {
    use framework::core::message::MessageRole as FrameworkRole;
    use crate::types::MessageRole;

    let role = match msg.role {
        MessageRole::User => FrameworkRole::User,
        MessageRole::Assistant => FrameworkRole::Assistant,
        MessageRole::System => FrameworkRole::System,
        MessageRole::Tool => FrameworkRole::Tool,
    };

    let mut framework_msg = FrameworkMessage {
        role,
        content: msg.content.clone(),
        tool_calls: None,
        tool_call_id: msg.tool_call_id.clone(),
    };

    // 转换工具调用（如果有的话）
    if let Some(local_tool_calls) = &msg.tool_calls {
        let framework_tool_calls: Vec<FrameworkToolCall> = local_tool_calls.iter()
            .map(|tc| FrameworkToolCall {
                id: tc.id.clone(),
                function: framework::core::message::ToolCallFunction {
                    name: tc.name.clone(),
                    arguments: tc.parameters.clone(),
                },
            })
            .collect();
        framework_msg.tool_calls = Some(framework_tool_calls);
    }

    framework_msg
}
