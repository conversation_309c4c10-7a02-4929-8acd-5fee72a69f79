[package]
name = "minimal_agent"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A minimal agent system based on LLM-driven tool calling"
license = "MIT"

[[bin]]
name = "minimal_agent"
path = "src/main.rs"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
async-trait = "0.1"
futures = "0.3"

# HTTP client for LLM API calls
reqwest = { version = "0.11", features = ["json", "stream"] }

# Streaming support
tokio-stream = "0.1"
eventsource-stream = "0.2"
pin-project-lite = "0.2"

# JSON serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# UUID generation
uuid = { version = "1.0", features = ["v4", "serde"] }

# Configuration
config = "0.14"

# CLI
clap = { version = "4.0", features = ["derive"] }
crossterm = "0.27"
indicatif = "0.17"
console = "0.15"
dialoguer = "0.11"
rustyline = "14.0"
dirs = "5.0"



# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }



[dev-dependencies]
tokio-test = "0.4"
